import requests
from bs4 import BeautifulSoup
import re
import json
import logging
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timezone
import random
import bleach
import os

# إنشاء مجلد temp إذا لم يكن موجودًا
os.makedirs('temp', exist_ok=True)

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class DiscudemyScraper:
    def __init__(self, base_url, max_pages=20, timeout=10, concurrent_requests=5):
        self.base_url = base_url
        self.max_pages = max_pages
        self.timeout = timeout
        self.concurrent_requests = concurrent_requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8'
        })

    def get_page_url(self, page_num):
        """إنشاء رابط الصفحة بناءً على رقم الصفحة"""
        if page_num == 1:
            return self.base_url
        return f"{self.base_url}/page/{page_num}"

    def extract_price(self, text):
        """استخراج السعر من النص"""
        # البحث عن نمط السعر (مثل $13.99 أو 13.99$)
        price_match = re.search(r'(\$\d+\.?\d*|\d+\.?\d*\$)', text)
        if price_match:
            price_str = price_match.group(1)
            # إزالة الرموز غير الرقمية للحصول على القيمة العددية
            price_value = float(re.sub(r'[^\d.]', '', price_str))
            return price_value, price_str
        return None, None

    def is_price_in_range(self, price_value):
        """التحقق مما إذا كان السعر ضمن النطاق المطلوب (أكبر من 0 دولار)"""
        if price_value is None or price_value <= 0:
            return False
        return price_value > 0

    def get_course_details(self, course_url):
        """الحصول على تفاصيل الدورة من صفحة الدورة"""
        try:
            response = self.session.get(course_url, timeout=self.timeout)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            # حفظ صفحة الدورة للتصحيح
            with open('temp/course_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)

            # استخراج العنوان - تحديث المحددات
            title_elem = soup.select_one('div.content div.header a.card-header, h1, h2, h3, .card-title, .course-title')
            title = title_elem.text.strip() if title_elem else "دورة Udemy"

            # استخراج الصورة المصغرة - تحديث المحددات
            img_elem = soup.select_one('div.image amp-img, img.ui.image, img.course-image, amp-img, img[src*="udemycdn"]')
            thumbnail = img_elem.get('src') if img_elem else None

            # إذا لم نجد الصورة، نبحث في النص HTML
            if not thumbnail:
                html_text = str(soup)
                img_pattern = r'https://img-c\.udemycdn\.com/course/\d+x\d+/[^"\'&\s]+'
                img_matches = re.findall(img_pattern, html_text)
                if img_matches:
                    thumbnail = img_matches[0]

            # استخراج السعر الأصلي - تحديث المحددات
            price_text = soup.select_one('span[style*="text-decoration: line-through"], .price_old, .original-price, .price, .course-price')
            original_price = "$49.99"  # قيمة افتراضية
            price_value = 49.99

            if price_text:
                price_value, price_str = self.extract_price(price_text.text)
                if price_str:
                    original_price = price_str

            # التحقق من أن السعر ضمن النطاق المطلوب (أكبر من 0 دولار)
            if not self.is_price_in_range(price_value):
                logger.warning(f"سعر الدورة {price_value} خارج النطاق المطلوب")
                # إذا كان السعر صفرًا أو سالبًا، نخطى هذه الدورة
                if price_value <= 0:
                    logger.warning(f"تم تخطي الدورة لأن سعرها {price_value} (صفر أو سالب)")
                    return None

            # تحويل رابط الدورة إلى رابط الكوبون مباشرة
            course_id = course_url.split('/')[-1]
            coupon_url = f"https://www.discudemy.com/go/{course_id}"

            logger.info(f"رابط الكوبون المباشر: {coupon_url}")

            # الحصول على صفحة الكوبون
            try:
                response = self.session.get(coupon_url, timeout=self.timeout)
                response.raise_for_status()
                coupon_soup = BeautifulSoup(response.text, 'html.parser')

                # حفظ صفحة الكوبون للتصحيح
                with open('temp/coupon_page.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)

                # البحث عن رابط Udemy - تحديث المحددات
                udemy_url = None

                # طريقة 1: البحث عن زر أخضر
                udemy_link = coupon_soup.select_one('a.ui.big.inverted.green.button, a.ui.big.green.button, a.btn-success, a.btn-primary, a.coupon-link')
                if udemy_link and 'href' in udemy_link.attrs:
                    udemy_url = udemy_link['href']

                # طريقة 2: البحث عن أي رابط يحتوي على udemy.com
                if not udemy_url:
                    for link in coupon_soup.select('a[href*="udemy.com"]'):
                        udemy_url = link.get('href')
                        if 'couponCode=' in udemy_url or 'COUPONCODE=' in udemy_url:
                            break

                # طريقة 3: البحث في النص HTML مباشرة
                if not udemy_url:
                    html_text = str(coupon_soup)
                    udemy_pattern = r'https://www\.udemy\.com/course/[^"\'&\s]+(?:couponCode|COUPONCODE)=[^"\'&\s]+'
                    udemy_matches = re.findall(udemy_pattern, html_text)
                    if udemy_matches:
                        udemy_url = udemy_matches[0]

                if udemy_url and ('couponCode=' in udemy_url or 'COUPONCODE=' in udemy_url):
                    logger.info(f"تم العثور على رابط Udemy: {udemy_url}")
                    return {
                        'title': title,
                        'link': udemy_url,
                        'thumbnail': thumbnail,
                        'original_price': original_price,
                        'price_value': price_value,
                        'coupons_left': 100,  # قيمة افتراضية للكوبونات المتبقية
                        'language': 'ar',  # افتراض أن الدورات عربية
                        'status': 'paid_with_coupon',  # حالة الكوبون
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"لم يتم العثور على رابط Udemy في صفحة الكوبون: {coupon_url}")

                    # محاولة استخدام رابط بديل
                    alt_coupon_url = f"https://www.discudemy.com/go-to/{course_id}"
                    try:
                        response = self.session.get(alt_coupon_url, timeout=self.timeout)
                        response.raise_for_status()
                        alt_coupon_soup = BeautifulSoup(response.text, 'html.parser')

                        # حفظ صفحة الكوبون البديلة للتصحيح
                        with open('temp/alt_coupon_page.html', 'w', encoding='utf-8') as f:
                            f.write(response.text)

                        # البحث عن رابط Udemy في الصفحة البديلة
                        udemy_url = None

                        # طريقة 1: البحث عن زر أخضر
                        udemy_link = alt_coupon_soup.select_one('a.ui.big.inverted.green.button, a.ui.big.green.button, a.btn-success, a.btn-primary, a.coupon-link')
                        if udemy_link and 'href' in udemy_link.attrs:
                            udemy_url = udemy_link['href']

                        # طريقة 2: البحث في النص HTML مباشرة
                        if not udemy_url:
                            html_text = str(alt_coupon_soup)
                            udemy_pattern = r'https://www\.udemy\.com/course/[^"\'&\s]+(?:couponCode|COUPONCODE)=[^"\'&\s]+'
                            udemy_matches = re.findall(udemy_pattern, html_text)
                            if udemy_matches:
                                udemy_url = udemy_matches[0]

                        if udemy_url and ('couponCode=' in udemy_url or 'COUPONCODE=' in udemy_url):
                            logger.info(f"تم العثور على رابط Udemy من الصفحة البديلة: {udemy_url}")
                            return {
                                'title': title,
                                'link': udemy_url,
                                'thumbnail': thumbnail,
                                'original_price': original_price,
                                'price_value': price_value,
                                'coupons_left': 100,  # قيمة افتراضية للكوبونات المتبقية
                                'language': 'ar',  # افتراض أن الدورات عربية
                                'status': 'paid_with_coupon',  # حالة الكوبون
                                'timestamp': datetime.now().isoformat()
                            }
                        else:
                            logger.warning(f"لم يتم العثور على رابط Udemy في صفحة الكوبون البديلة: {alt_coupon_url}")
                    except Exception as e:
                        logger.error(f"خطأ في الحصول على صفحة الكوبون البديلة: {e}")

            except Exception as e:
                logger.error(f"خطأ في الحصول على رابط الكوبون: {e}")

            return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل الدورة: {e}")
            return None

    def process_page(self, page_num):
        """معالجة صفحة واحدة من الدورات"""
        courses = []
        try:
            url = self.get_page_url(page_num)
            logger.info(f"معالجة الصفحة {page_num}: {url}")

            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            # حفظ صفحة القائمة للتصحيح
            with open(f'temp/page_{page_num}.html', 'w', encoding='utf-8') as f:
                f.write(response.text)

            # البحث عن جميع الدورات في الصفحة - تحديث المحدد CSS
            course_cards = soup.select('section.card, .card, .course-card, article')
            logger.info(f"تم العثور على {len(course_cards)} دورة في الصفحة {page_num}")

            for card in course_cards:
                try:
                    # استخراج رابط الدورة - تحديث المحددات
                    link_elem = card.select_one('a.card-header, a[href*="/course/"], h3 a, .card-title a, .course-title a')
                    if not link_elem:
                        continue

                    course_url = link_elem.get('href')
                    if not course_url:
                        continue

                    # التأكد من أن الرابط كامل
                    if not course_url.startswith(('http://', 'https://')):
                        course_url = f"https://www.discudemy.com{course_url}" if not course_url.startswith('/') else f"https://www.discudemy.com{course_url}"

                    logger.info(f"معالجة الدورة: {course_url}")

                    # الحصول على تفاصيل الدورة
                    course_details = self.get_course_details(course_url)
                    if course_details:
                        courses.append(course_details)
                        logger.info(f"تمت إضافة دورة: {course_details['title']}")
                except Exception as e:
                    logger.error(f"خطأ في معالجة الدورة: {e}")
                    continue

            return courses
        except Exception as e:
            logger.error(f"خطأ في معالجة الصفحة {page_num}: {e}")
            return []

    async def fetch_all_courses(self):
        """جلب جميع الدورات باستخدام المعالجة المتوازية"""
        all_courses = []
        start_time = time.time()
        logger.info(f"بدء جلب الدورات من {self.max_pages} صفحة...")

        # استخدام ThreadPoolExecutor للمعالجة المتوازية
        with ThreadPoolExecutor(max_workers=self.concurrent_requests) as executor:
            # إنشاء مهام لكل صفحة
            futures = [executor.submit(self.process_page, page) for page in range(1, self.max_pages + 1)]

            # انتظار اكتمال جميع المهام
            for future in futures:
                try:
                    courses = future.result()
                    all_courses.extend(courses)
                except Exception as e:
                    logger.error(f"خطأ في معالجة الصفحة: {e}")

        elapsed_time = time.time() - start_time
        logger.info(f"تم جلب {len(all_courses)} دورة في {elapsed_time:.2f} ثانية")

        return all_courses

    def save_courses(self, courses, filename):
        """حفظ الدورات في ملف JSON"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)
            logger.info(f"تم حفظ {len(courses)} دورة في {filename}")
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ الدورات: {e}")
            return False

async def main():
    """الدالة الرئيسية"""
    print("بدء استخراج الدورات من موقع Discudemy...")

    # إنشاء كائن من الفئة
    scraper = DiscudemyScraper(
        base_url='https://www.discudemy.com/language/arabic',
        max_pages=20,
        timeout=10,
        concurrent_requests=5
    )

    # استخراج الدورات
    courses = await scraper.fetch_all_courses()

    print(f"تم استخراج {len(courses)} دورة من موقع Discudemy")

    # حفظ الدورات في ملف JSON
    if scraper.save_courses(courses, 'discudemy_courses.json'):
        print("تم حفظ الدورات بنجاح")
    else:
        print("حدث خطأ أثناء حفظ الدورات")

if __name__ == "__main__":
    # تشغيل الدالة الرئيسية
    asyncio.run(main())












